# -*- coding: utf-8 -*-
"""
UniSolver Configuration

Centralized configuration for the UniSolver system with optimized parameters
for better accuracy and user experience.
"""

# Verification Configuration
VERIFICATION_CONFIG = {
    # Verification thresholds (more lenient for better guidance)
    'max_failure_rate_pass': 0.5,  # Allow up to 50% failure rate to pass
    'correction_threshold': 0.7,   # Only attempt correction if >70% failed
    'max_correction_attempts': 2,  # Reduced from 3 to 2 for efficiency
    
    # Question generation limits (reduced for focus)
    'max_questions_per_category': {
        'numerical_verification': 2,
        'constraint_verification': 2, 
        'objective_verification': 1,
        'logic_verification': 1
    },
    
    # Verification approach
    'verification_mode': 'constructive',  # 'strict' or 'constructive'
    'focus_on_guidance': True,  # Focus on providing guidance rather than rejection
}

# Agent Configuration
AGENT_CONFIG = {
    # Default model for all agents
    'default_model': 'deepseek-reasoner',
    
    # Information Agent
    'information_agent': {
        'max_depth': 2,
        'max_search_results': 3,  # Reduced for efficiency
        'enable_hierarchical': True
    },
    
    # Modeling Agent  
    'modeling_agent': {
        'max_iterations': 2,  # Reduced from 3
        'enable_simplified_mode': True,
        'focus_on_completeness': True
    },
    
    # Code Agent
    'code_agent': {
        'enable_detailed_output': True,
        'include_error_handling': True,
        'force_utf8_encoding': True
    },
    
    # Repair Agent
    'repair_agent': {
        'max_code_repairs': 3,
        'max_model_reconstructions': 2,
        'enable_dual_layer': True
    }
}

# System Configuration
SYSTEM_CONFIG = {
    # Performance settings
    'enable_progress_tracking': True,
    'save_intermediate_results': True,
    'enable_detailed_logging': True,
    
    # Output settings
    'results_directory': 'results',
    'enable_rich_output': True,
    'auto_save_results': True,
    
    # Error handling
    'graceful_degradation': True,
    'continue_on_errors': True
}

# Evaluation Configuration
EVALUATION_CONFIG = {
    # Accuracy calculation
    'tolerance_for_numerical_answers': 1e-6,
    'enable_partial_credit': True,
    
    # Success criteria
    'minimum_pass_rate': 0.6,  # 60% pass rate target
    'target_accuracy_rate': 0.8,  # 80% accuracy target
    
    # Reporting
    'detailed_error_analysis': True,
    'generate_improvement_suggestions': True
}

def get_config(section: str = None):
    """Get configuration for a specific section or all configurations."""
    configs = {
        'verification': VERIFICATION_CONFIG,
        'agent': AGENT_CONFIG,
        'system': SYSTEM_CONFIG,
        'evaluation': EVALUATION_CONFIG
    }
    
    if section:
        return configs.get(section, {})
    return configs

def update_config(section: str, key: str, value):
    """Update a specific configuration value."""
    configs = get_config()
    if section in configs and key in configs[section]:
        configs[section][key] = value
        return True
    return False