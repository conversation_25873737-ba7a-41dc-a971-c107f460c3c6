# -*- coding: utf-8 -*-
import re
import subprocess
import sys
import tempfile
import os
import openai
import requests
import arxiv
import shutil
import wcwidth
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Initialize OpenAI client
client = openai.OpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_API_BASE")
)

def is_number_string(s):
    """
    Check if a string is a numeric string, including integers and decimals.

    Args:
    s: The string to check.

    Returns:
    True if the string is numeric, False otherwise.
    """
    pattern = r"^[-+]?\d+(\.\d+)?$"  # Regular expression to match integers or decimals
    return re.match(pattern, s) is not None

def convert_to_number(s):
    """
    Convert a string to a number (integer or float).

    Args:
        s: The string to convert.

    Returns:
        int or float: Returns int if string represents integer, float if decimal.
        Returns None if conversion fails.
    """
    try:
        # Try to convert to integer
        if s.isdigit() or (s.startswith('-') and s[1:].isdigit()):
            return int(s)
        # Try to convert to float
        num = float(s)
        return num
    except (ValueError, TypeError):
        return None

def extract_best_objective(output_text):
    """
    Extract Best objective or Optimal objective value from Gurobi output.
    Enhanced to support multiple output formats.
    
    Args:
        output_text: Gurobi output text
    
    Returns:
        float or None: Optimal solution value, None if not found
    """
    # First check if model is infeasible
    if "Model is infeasible" in output_text or "INFEASIBLE" in output_text:
        return None
    
    # Define multiple patterns to match different output formats
    patterns = [
        r'Best objective\s+([\d.e+-]+)',
        r'Optimal objective\s+([\d.e+-]+)', 
        r'Objective value:\s*([\d.e+-]+)',
        r'Optimal solution value:\s*([\d.e+-]+)',
        r'Best bound:\s*([\d.e+-]+)',
        r'Obj:\s*([\d.e+-]+)',
        r'Objective:\s*([\d.e+-]+)',
        r'Solution value:\s*([\d.e+-]+)',
        r'Optimal value:\s*([\d.e+-]+)',
        r'Final objective:\s*([\d.e+-]+)'
    ]
    
    # Try each pattern
    for pattern in patterns:
        match = re.search(pattern, output_text, re.IGNORECASE)
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                continue
    
    # If no pattern matches, try to find any number after common keywords
    fallback_patterns = [
        r'(?:objective|value|solution|optimal|best).*?([\d.e+-]+)',
        r'([\d.e+-]+).*?(?:objective|optimal|best)'
    ]
    
    for pattern in fallback_patterns:
        matches = re.findall(pattern, output_text, re.IGNORECASE)
        if matches:
            try:
                return float(matches[0])
            except ValueError:
                continue
    
    return None

def extract_and_execute_python_code(text_content):
    """
    Extract Python code blocks from text and execute them.

    Args:
        text_content: Text content containing code blocks.

    Returns:
        bool: True if execution was successful, False otherwise
        str: Error message if execution failed, best objective if successful
    """
    python_code_blocks = re.findall(r'```python\s*([\s\S]*?)```', text_content)

    if not python_code_blocks:
        print("No Python code blocks found.")
        return False, "No Python code blocks found"

    for code_block in python_code_blocks:
        code_block = code_block.strip()
        if not code_block:
            print("Found empty Python code block, skipped.")
            continue

        print("Found Python code block, starting execution...")
        try:
            with tempfile.NamedTemporaryFile(mode="w", suffix=".py", delete=False, encoding='utf-8') as tmp_file:
                tmp_file.write("# -*- coding: utf-8 -*-\n")
                tmp_file.write(code_block)
                temp_file_path = tmp_file.name

            result = subprocess.run([sys.executable, temp_file_path], 
                           capture_output=True, 
                           text=True, 
                           encoding='utf-8', 
                           errors='ignore', 
                           check=False)

            if result.returncode == 0:
                print("Python code executed successfully, output:\n")
                print(result.stdout)
                
                best_obj = extract_best_objective(result.stdout)
                if best_obj is not None:
                    print(f"\nBest objective value: {best_obj}")
                else:
                    print("\nNo optimal solution value found")
                return True, str(best_obj)
            else:
                print(f"Python code execution error:\n")
                print(result.stderr)
                return False, result.stderr

        except Exception as e:
            print(f"Error executing Python code block: {e}")
            return False, str(e)
        finally:
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.remove(temp_file_path)
        print("-" * 30)

    return False, "No valid code blocks executed"

def eval_model_result(success, result, ground_truth, err_range=None):
    """Evaluate model result with adaptive error tolerance."""
    pass_flag = False
    correct_flag = False
    
    if success:
        pass_flag = True
        if is_number_string(str(result)) and ground_truth is not None:
            result_num = convert_to_number(str(result))
            ground_truth_num = convert_to_number(str(ground_truth))
            
            # Adaptive error range based on magnitude
            if err_range is None:
                if abs(ground_truth_num) < 1:
                    err_range = 0.01  # 1% for small numbers
                elif abs(ground_truth_num) < 100:
                    err_range = 1.0   # 1 unit for medium numbers
                elif abs(ground_truth_num) < 10000:
                    err_range = abs(ground_truth_num) * 0.05  # 5% for large numbers
                else:
                    err_range = abs(ground_truth_num) * 0.1   # 10% for very large numbers
            
            # Check if within tolerance
            if abs(result_num - ground_truth_num) <= err_range:
                correct_flag = True
            else:
                # Additional check for relative error
                if ground_truth_num != 0:
                    relative_error = abs(result_num - ground_truth_num) / abs(ground_truth_num)
                    if relative_error <= 0.05:  # 5% relative error tolerance
                        correct_flag = True
                        
        elif result == 'None' or result is None: # no available solution
            if ground_truth is None or ground_truth == 'None':
                correct_flag = True
    return pass_flag, correct_flag


def query_llm(messages, model_name="deepseek-reasoner", temperature=0.2):
    """
    Call LLM to get response results using streaming output.
    
    Args:
        messages (list): Conversation context list.
        model_name (str): LLM model name, default is "deepseek-reasoner".
        temperature (float): Controls the randomness of output, default is 0.2.

    Returns:
        str: Response content generated by LLM.
    """
    # Use stream=True to enable streaming output
    response = client.chat.completions.create(
        model=model_name,
        messages=messages,
        temperature=temperature,
        stream=True
    )
    
    # For accumulating complete response
    full_response = ""
    
    # Control print format
    print("LLM Output:")
    
    # Process streaming response chunk by chunk
    current_line = ""
    for chunk in response:
        # First check if choices list is not empty
        if hasattr(chunk, 'choices') and len(chunk.choices) > 0:
            # Then check if there are delta and content
            if hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content'):
                content = chunk.choices[0].delta.content
                if content:
                    current_line += content
                    full_response += content
                    
                    # Output current line when encountering newline or punctuation
                    if '\n' in current_line or any(punct in current_line for punct in ['。', '！', '？', '.', '!', '?', ':', '：']):
                        print(current_line, end="", flush=True)
                        current_line = ""
    
    # Output remaining content
    if current_line:
        print(current_line, end="", flush=True)
    
    # Add newline after output is complete
    print()
    
    return full_response


def google_custom_search(query, max_results=3):
    """Use Google Custom Search API to search"""
    api_key = os.getenv("GOOGLE_API_KEY")
    cx = os.getenv("GOOGLE_CSE_ID")  # Custom Search Engine ID
    
    if not api_key or not cx:
        print("Google Custom Search API not configured, skipping API search")
        return []
    
    try:
        base_url = "https://www.googleapis.com/customsearch/v1"
        params = {
            'q': query,
            'key': api_key,
            'cx': cx,
            'num': min(max_results, 10)
        }
        
        response = requests.get(base_url, params=params)
        data = response.json()
        
        # Validate response data structure
        if response.status_code != 200:
            print(f"API request failed: {data.get('error', {}).get('message', 'Unknown error')}")
            return []
        
        if 'items' in data:
            return [f"{item['title']}: {item.get('snippet', '')[:200]}..." for item in data['items']]
        else:
            print(f"Google Custom Search API returned no results: {data.get('error', {}).get('message', 'Unknown error')}")
            return []
    except Exception as e:
        print(f"Google Custom Search API search failed: {e}")
        return []


def arxiv_search(query, max_results=3):
    """ArXiv API search for latest papers with proper error handling"""
    try:
        # Translate Chinese query to English for better arXiv search results
        if any('\u4e00' <= char <= '\u9fff' for char in query):
            # Contains Chinese characters, translate to English
            english_query = translate_to_english(query)
            if english_query:
                query = english_query
            else:
                # Fallback to common English terms for mathematical optimization
                query = "mathematical optimization linear programming"
        
        # Configure arXiv client with proper settings
        client_arxiv = arxiv.Client(
            page_size=max_results,
            delay_seconds=1.0,
            num_retries=3
        )
        
        search = arxiv.Search(
            query=query,
            max_results=max_results,
            sort_by=arxiv.SortCriterion.SubmittedDate,
            sort_order=arxiv.SortOrder.Descending
        )
        
        results = list(client_arxiv.results(search))
        return [f"{result.title}: {result.summary[:200]}..." for result in results]
        
    except Exception as e:
        print(f"ArXiv search failed: {e}")
        return []


def translate_to_english(chinese_text):
    """Simple translation helper for Chinese to English"""
    translation_map = {
        "linear programming": "linear programming",
        "integer programming": "integer programming", 
        "optimization": "optimization",
        "mathematical modeling": "mathematical modeling",
        "operations research": "operations research",
        "constraint": "constraint",
        "objective function": "objective function",
        "decision variable": "decision variable",
        "solving": "solving",
        "algorithm": "algorithm"
    }
    
    english_terms = []
    for chinese, english in translation_map.items():
        if chinese in chinese_text:
            english_terms.append(english)
    
    return " ".join(english_terms) if english_terms else None


def clean_code_from_markdown(code: str) -> str:
    """
    Clean Python code by removing markdown formatting.
    
    Args:
        code (str): Code that may contain markdown formatting
        
    Returns:
        str: Clean Python code without markdown formatting
    """
    # Remove ```python and ``` markers
    code = re.sub(r'^```python\s*\n?', '', code, flags=re.MULTILINE)
    code = re.sub(r'\n?```\s*$', '', code, flags=re.MULTILINE)
    code = re.sub(r'^```\s*\n?', '', code, flags=re.MULTILINE)
    
    # Remove any remaining ``` markers
    code = code.replace('```', '')
    
    return code.strip()


def execute_in_sandbox(code: str) -> tuple:
    """
    Execute Python code in an isolated environment using subprocess and temporary file.
    
    Args:
        code (str): Python code to execute (may contain markdown formatting)
        
    Returns:
        tuple: (result_dict, error_message)
            - If successful: (result_dict, None) where result_dict contains status, result, output
            - If failed: (None, error_message)
    """
    try:
        # Clean the code from any markdown formatting
        clean_code = clean_code_from_markdown(code)
        
        with tempfile.NamedTemporaryFile(mode="w", suffix=".py", delete=False, encoding='utf-8') as tmp_file:
            tmp_file.write("# -*- coding: utf-8 -*-\n")
            tmp_file.write(clean_code)
            temp_file_path = tmp_file.name

        result = subprocess.run([sys.executable, temp_file_path], 
                       capture_output=True, 
                       text=True, 
                       encoding='utf-8', 
                       errors='ignore', 
                       check=False,
                       timeout=60)  # 60 second timeout

        if result.returncode == 0:
            best_obj = extract_best_objective(result.stdout)
            result_dict = {
                "status": "success",
                "result": best_obj,
                "output": result.stdout
            }
            return result_dict, None
        else:
            return None, result.stderr

    except subprocess.TimeoutExpired:
        return None, "Code execution timed out after 60 seconds"
    except Exception as e:
        return None, str(e)
    finally:
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            os.remove(temp_file_path)


def get_display_width(text):
    """
    Calculate the display width of a string, considering wide characters like Chinese.
    Use the wcwidth module for precise width calculation.

    Args:
        text (str): The text to calculate width for.

    Returns:
        int: The display width of the text.
    """
    return wcwidth.wcswidth(text)


def print_header(text="", add_newline_before=True, add_newline_after=True, 
                border_char="=", side_char="||"):
    """
    Print a header with customizable text in the middle, adjusted to console window width.
    Correctly handles wide characters like Chinese.

    Args:
        text (str): The text to display in the middle of the header.
        add_newline_before (bool): Whether to add a newline before the header.
        add_newline_after (bool): Whether to add a newline after the header.
        border_char (str): The character used for top and bottom borders.
        side_char (str): The character used for side borders.
    """
    # Add a newline before the header if requested
    if add_newline_before:
        print()
    
    # Get terminal width
    terminal_width = shutil.get_terminal_size().columns
    
    # Ensure minimum width
    terminal_width = max(terminal_width, 40)
    
    # Calculate side character padding
    side_char_len = len(side_char)
    
    # Print the top border
    print(border_char * terminal_width)
    
    # Print the empty line
    print(side_char + " " * (terminal_width - 2 * side_char_len) + side_char)
    
    # Print the middle line with text
    text_display_width = get_display_width(text)
    available_space = terminal_width - 2 * side_char_len
    
    if text_display_width <= available_space:
        left_padding = (available_space - text_display_width) // 2
        right_padding = available_space - text_display_width - left_padding
        print(side_char + " " * left_padding + text + " " * right_padding + side_char)
    else:
        # If text is too long, we need to truncate it
        # This is more complex with wide characters, so we'll do it character by character
        truncated_text = ""
        truncated_width = 0
        for char in text:
            char_width = get_display_width(char)
            if truncated_width + char_width + 3 > available_space:  # +3 for the "..."
                break
            truncated_text += char
            truncated_width += char_width
        
        truncated_text += "..."
        right_padding = available_space - get_display_width(truncated_text)
        print(side_char + truncated_text + " " * right_padding + side_char)
    
    # Print the empty line
    print(side_char + " " * (terminal_width - 2 * side_char_len) + side_char)
    
    # Print the bottom border
    print(border_char * terminal_width)
    
    # Add a newline after the header if requested
    if add_newline_after:
        print()