# -*- coding: utf-8 -*-
"""
Verifier Agent - Unified Answer Generation, Verification and Correction

This agent is the final gatekeeper responsible for:
1. Generating initial structured answers
2. Creating and answering verification questions
3. Performing iterative correction based on verification results
4. Outputting final validated and formatted answers
"""

import json
from typing import Dict, List
from utils import query_llm
from config import get_config


class VerifierAgent:
    """Unified agent for answer generation, verification, correction and final formatting."""
    
    def __init__(self, model_name="deepseek-reasoner"):
        self.model_name = model_name
        # Load configuration
        self.config = get_config('verification')
        self.max_correction_attempts = self.config.get('max_correction_attempts', 2)
        self.max_failure_rate_pass = self.config.get('max_failure_rate_pass', 0.5)
        self.correction_threshold = self.config.get('correction_threshold', 0.7)
    
    def generate_initial_answer(self, problem: str, knowledge_matrix: Dict, solution: Dict) -> str:
        """Generate structured initial answer from solver output."""
        print("[VerifierAgent] Generating initial answer...")
        
        messages = [
            {"role": "system", "content": (
                "You are an operations research expert skilled in presenting complex optimization results clearly to decision-makers.\n"
                "Generate a structured initial answer based on the provided solver output for the optimization problem. "
                "The answer must be precise, exclusively based on the provided data, and formatted in English.\n"
                "Do not add any analysis, interpretation, or information not directly present in the solver output. "
                "This output will be used for automated verification."
            )},
            {"role": "user", "content": f"**Problem Description:**\n{problem}\n\n**Solver Output:**\n{solution}\n\n**Knowledge Matrix Summary:**\n{knowledge_matrix}"},
            {"role": "user", "content": (
                "Generate the initial answer with the following three sections:\n"
                "1. **Executive Summary:** A brief one-paragraph overview of the problem's objective and the outcome of the solution.\n"
                "2. **Optimal Solution Details:** A detailed breakdown of the decision variables and their final values ($X_{opt}$). Present this in a clear, organized format (e.g., a list or table).\n"
                "3. **Objective Value:** Clearly state the final objective function value ($Z_{opt}$) and specify whether it was a minimization or maximization task."
            )}
        ]
        
        response = query_llm(messages, self.model_name)
        
        # Clean up the response to ensure it's pure JSON
        response = response.strip()
        
        # Remove markdown code block markers if present
        if response.startswith('```json'):
            response = response[7:]
        if response.startswith('```'):
            response = response[3:]
        if response.endswith('```'):
            response = response[:-3]
        
        response = response.strip()
        
        # Validate that it's valid JSON
        try:
            json.loads(response)
            return response
        except json.JSONDecodeError as e:
            print(f"[VerifierAgent] Warning: Generated response is not valid JSON: {e}")
            print(f"[VerifierAgent] Raw response: {response[:200]}...")
            return response
    
    def generate_verification_questions(self, problem: str, initial_answer: str, knowledge_matrix: Dict) -> Dict:
        """Generate verification questions across four dimensions with adaptive difficulty."""
        print("[VerifierAgent] Generating adaptive verification questions...")
        
        messages = [
            {"role": "system", "content": (
                "You are a QA expert focused on constructive verification. Your task is to generate verification questions "
                "that help identify and guide improvements rather than simply rejecting answers. "
                "Questions should be answerable and focused on common optimization problem aspects."
            )},
            {"role": "user", "content": f"**Original Problem:**\n{problem}\n\n**Initial Answer to Verify:**\n{initial_answer}\n\n**Knowledge Matrix (for constraint reference):**\n{knowledge_matrix}"},
            {"role": "user", "content": (
                "Generate a JSON object with verification questions in four categories. "
                "IMPORTANT: Generate 2-3 questions per category maximum, focusing on the most essential aspects.\n"
                "Make questions constructive and answerable based on the provided information.\n\n"
                "- **A (Numerical Verification):** 2 basic calculation checks (e.g., does the objective value match the solution?)\n"
                "- **B (Constraint Verification):** 2 key constraint checks (focus on most obvious constraints)\n"
                "- **C (Objective Verification):** 1 simple optimality check (is the objective correctly stated?)\n"
                "- **D (Logic Verification):** 1 practical reasonableness check (does the solution make business sense?)\n"
                "**Output Format (JSON only):**\n"
                "{\n"
                "  \"numerical_verification\": [ \"Question 1: ...\", \"Question 2: ...\" ],\n"
                "  \"constraint_verification\": [ \"Question 1: ...\", \"Question 2: ...\" ],\n"
                "  \"objective_verification\": [ \"Question 1: ...\" ],\n"
                "  \"logic_verification\": [ \"Question 1: ...\" ]\n"
                "}"
            )}
        ]
        
        response = query_llm(messages, self.model_name)
        
        # Clean up the response to ensure it's pure JSON
        response = response.strip()
        
        # Remove markdown code block markers if present
        if response.startswith('```json'):
            response = response[7:]
        if response.startswith('```'):
            response = response[3:]
        if response.endswith('```'):
            response = response[:-3]
        
        response = response.strip()
        
        try:
            return json.loads(response)
        except json.JSONDecodeError as e:
            print(f"[VerifierAgent] Error parsing verification questions JSON: {e}")
            print(f"[VerifierAgent] Raw response: {response[:200]}...")
            return {}
    
    def answer_verification_question(self, problem: str, initial_answer: str, knowledge_matrix: Dict, question: str) -> Dict:
        """Answer a single verification question with constructive feedback."""
        print(f"[VerifierAgent] Answering question: {question[:50]}...")
        
        messages = [
            {"role": "system", "content": (
                "You are a constructive analyst focused on providing helpful feedback. "
                "Your goal is to identify areas for improvement while being fair and reasonable. "
                "Only mark as FAIL if there are clear, significant errors that need correction."
            )},
            {"role": "user", "content": f"**Context:**\n- **Original Problem:** {problem}\n- **Initial Answer:** {initial_answer}\n- **Knowledge Matrix:** {knowledge_matrix}"},
            {"role": "user", "content": f"**Verification Question to Answer:**\n\"{question}\""},
            {"role": "user", "content": (
                "Provide your response in a JSON object with the following fields:\n"
                "1. **reasoning:** A constructive analysis focusing on what works and what could be improved.\n"
                "2. **conclusion:** A balanced assessment of the verification question.\n"
                "3. **status:** Use \"PASS\" unless there are clear, significant errors. Use \"FAIL\" only for major issues.\n"
                "**Guidelines for status:**\n"
                "- PASS: Answer is reasonable, mostly correct, or minor issues that don't affect core solution\n"
                "- FAIL: Major errors, missing critical information, or fundamentally incorrect approach\n"
                "**Output Format (JSON only):**\n"
                "{\n"
                "  \"reasoning\": \"...\",\n"
                "  \"conclusion\": \"...\",\n"
                "  \"status\": \"PASS\"\n"
                "}"
            )}
        ]
        
        response = query_llm(messages, self.model_name)
        
        # Clean up the response to ensure it's pure JSON
        response = response.strip()
        
        # Remove markdown code block markers if present
        if response.startswith('```json'):
            response = response[7:]
        if response.startswith('```'):
            response = response[3:]
        if response.endswith('```'):
            response = response[:-3]
        
        response = response.strip()
        
        try:
            return json.loads(response)
        except json.JSONDecodeError as e:
            print(f"[VerifierAgent] Error parsing answer JSON: {e}")
            print(f"[VerifierAgent] Raw response: {response[:200]}...")
            return {"reasoning": "JSON parsing failed", "conclusion": "Unable to parse response", "status": "FAIL"}
    
    def correct_answer(self, problem: str, knowledge_matrix: Dict, solution: Dict, 
                      initial_answer: str, failed_verifications: List[Dict]) -> str:
        """Correct the answer based on failed verification results."""
        print("[VerifierAgent] Correcting answer based on verification failures...")
        
        # Extract failure details
        failure_details = []
        for fail in failed_verifications:
            failure_details.append(f"Question: {fail['question']}")
            failure_details.append(f"Issue: {fail['answer']['reasoning']}")
            failure_details.append(f"Conclusion: {fail['answer']['conclusion']}")
        
        messages = [
            {"role": "system", "content": (
                "You are an expert in optimization problem solving and answer correction. "
                "Your task is to improve the initial answer based on specific verification failures."
            )},
            {"role": "user", "content": f"**Original Problem:**\n{problem}\n\n**Solution Data:**\n{solution}\n\n**Knowledge Matrix:**\n{knowledge_matrix}"},
            {"role": "user", "content": f"**Initial Answer:**\n{initial_answer}"},
            {"role": "user", "content": f"**Verification Failures:**\n" + "\n".join(failure_details)},
            {"role": "user", "content": (
                "Please generate a corrected answer that addresses all the verification failures. "
                "Maintain the same structure as the initial answer but fix the identified issues. "
                "Ensure accuracy, completeness, and logical consistency."
            )}
        ]
        
        return query_llm(messages, self.model_name)
    
    def generate_final_structured_answer(self, problem: str, knowledge_matrix: Dict, 
                                        solution: Dict, verified_answer: str, 
                                        verification_summary: str) -> str:
        """Generate the final structured JSON answer for both human reading and machine parsing."""
        print("[VerifierAgent] Generating final structured JSON answer...")
        
        messages = [
            {"role": "system", "content": (
                "You are a senior OR consultant. Your task is to generate the FINAL, validated decision report. "
                "This report MUST be a single JSON object containing both a human-readable summary and machine-readable structured data."
            )},
            {"role": "user", "content": f"**Original Problem:** {problem}\n\n**Original Solver Output:** {solution}\n\n**Final Verified Natural Language Answer:** {verified_answer}\n\n**Verification Process Summary:** {verification_summary}"},
            {"role": "user", "content": (
                "Based on all the provided context, generate a single, clean JSON object. \n\n"
                "CRITICAL: Your response must be ONLY the JSON object, with NO markdown formatting, NO code blocks, NO ```json tags, NO explanations. Start directly with { and end with }.\n\n"
                "The JSON object must have the following structure:\n"
                "{\n"
                "  \"human_readable_report\": {\n"
                "    \"title\": \"Optimization Solution Report for: [A concise title for the problem]\",\n"
                "    \"executive_summary\": \"A brief summary of the findings and the final recommendation. This should be based on the 'Final Verified Natural Language Answer'.\",\n"
                "    \"solution_details\": \"A detailed explanation of the optimal decision variables and their practical meaning. This should be based on the 'Final Verified Natural Language Answer'.\",\n"
                "    \"verification_notes\": \"A brief note confirming the robustness of the solution, based on the 'Verification Process Summary'.\"\n"
                "  },\n"
                "  \"machine_readable_data\": {\n"
                "    \"status\": \"[The final status of the solution, e.g., 'OPTIMAL', 'INFEASIBLE', 'UNBOUNDED']\",\n"
                "    \"objective_value\": [The single, final, numerical objective value. Should be a float or integer. If the solution is infeasible or not found, this MUST be null.],\n"
                "    \"decision_variables\": {\n"
                "      \"[variable_name_1]\": [value_1],\n"
                "      \"[variable_name_2]\": [value_2]\n"
                "    }\n"
                "  }\n"
                "}\n\n"
                "CRITICAL INSTRUCTIONS:\n"
                "1. The `objective_value` field is the MOST IMPORTANT. It must contain only the final numerical result for automated evaluation.\n"
                "2. If the solver's result was None or indicated an infeasible model, the `objective_value` must be `null`.\n"
                "3. Populate the `human_readable_report` using the content from the `final_verified_answer`.\n"
                "4. Your response must be ONLY the JSON object, starting with { and ending with }. NO other text."
            )}
        ]
        
        return query_llm(messages, self.model_name)
    
    def run(self, problem: str, knowledge_matrix: Dict, solution: Dict) -> str:
        """Run the complete verification and answer generation process."""
        print("[VerifierAgent] Starting unified answer generation and verification process...")
        
        # Step 1: Generate initial answer
        current_answer = self.generate_initial_answer(problem, knowledge_matrix, solution)
        
        # Step 2: Iterative verification and correction loop
        correction_attempts = 0
        verification_history = []
        
        while correction_attempts <= self.max_correction_attempts:
            print(f"[VerifierAgent] Verification attempt {correction_attempts + 1}")
            
            # Generate verification questions
            questions = self.generate_verification_questions(problem, current_answer, knowledge_matrix)
            
            # Collect all questions
            all_questions = []
            for category in questions.values():
                all_questions.extend(category)
            
            # Answer verification questions
            results = []
            fails = []
            for q in all_questions:
                ans = self.answer_verification_question(problem, current_answer, knowledge_matrix, q)
                results.append(ans)
                if ans['status'] == 'FAIL':
                    fails.append({'question': q, 'answer': ans})
            
            # Calculate failure rate
            total = len(all_questions)
            fail_count = len(fails)
            fail_rate = fail_count / total if total > 0 else 0
            
            verification_summary = f"Attempt {correction_attempts + 1}: {fail_count}/{total} failed ({fail_rate:.2%})"
            verification_history.append(verification_summary)
            
            print(f"[VerifierAgent] {verification_summary}")
            
            # Check if verification passed using configured criteria
            if fail_rate <= self.max_failure_rate_pass:
                print(f"[VerifierAgent] Verification passed! (Failure rate: {fail_rate:.1%})")
                break
            elif correction_attempts < self.max_correction_attempts and fail_rate > self.correction_threshold:
                print(f"[VerifierAgent] High failure rate ({fail_rate:.1%}), attempting correction...")
                current_answer = self.correct_answer(problem, knowledge_matrix, solution, current_answer, fails)
                correction_attempts += 1
            else:
                print(f"[VerifierAgent] Verification completed with {fail_rate:.1%} failure rate")
                break
        
        # Step 3: Generate final structured JSON answer
        final_verification_summary = "\n".join(verification_history)
        final_answer = self.generate_final_structured_answer(
            problem, knowledge_matrix, solution, current_answer, final_verification_summary
        )
        
        print("[VerifierAgent] Unified process completed")
        return final_answer