# -*- coding: utf-8 -*-
"""
Code Agent

This agent generates Gurobi Python code from mathematical models.
It takes a mathematical model and converts it into executable Python code
using the Gurobi optimization library.
"""

from utils import query_llm


class CodeAgent:
    """Agent responsible for generating Gurobi Python code."""
    
    def __init__(self, model_name="deepseek-reasoner"):
        """
        Initialize the Code Agent.
        
        Args:
            model_name (str): LLM model name to use for processing
        """
        self.model_name = model_name
    
    def run(self, math_model: str) -> str:
        """
        Generate Gurobi Python code from a mathematical model.
        
        Args:
            math_model (str): Mathematical model in LaTeX or text format
            
        Returns:
            str: Complete Python code using Gurobi
        """
        # Initialize conversation context
        messages = [
            {"role": "system", "content": (
                "You are a Gurobi optimization programming expert. Your task is to convert mathematical models into complete executable Python code.\n"
                "STRICT REQUIREMENTS:\n"
                "1. Use ONLY the Gurobi library to implement the optimization model\n"
                "2. Include complete model construction, variable definition, constraint addition, and solving process\n"
                "3. The code MUST be able to run directly and output results\n"
                "4. Include proper error handling and result output\n"
                "5. Use UTF-8 encoding and English comments only\n"
                "6. Follow standardized code structure and formatting\n"
                "7. Ensure all variable names and comments are in English\n"
                "8. MANDATORY: Enable detailed Gurobi solver output by setting model.setParam('OutputFlag', 1) and model.setParam('LogToConsole', 1)\n"
                "9. MANDATORY: Print detailed solving information including iteration process, solver status, and optimal objective value\n"
                "10. MANDATORY: Always print the final result in the format 'Optimal objective: [value]' or 'Best objective: [value]'"
            )}
        ]
        
        # Add the mathematical model
        messages.append({"role": "user", "content": f"Mathematical model:\n{math_model}"})
        
        # Request code generation
        print("[CodeAgent] Generating Gurobi Python code...")
        messages.append({"role": "user", "content": (
            "Based on the above mathematical model, write complete and reliable Python code using Gurobi to solve this optimization problem.\n"
            "MANDATORY REQUIREMENTS:\n"
            "1. Start with proper UTF-8 encoding declaration: # -*- coding: utf-8 -*-\n"
            "2. Include all necessary imports (import gurobipy as gp, from gurobipy import GRB)\n"
            "3. Use English variable names and comments only\n"
            "4. Include proper error handling with try-except blocks\n"
            "5. Output the optimal solution value clearly\n"
            "6. Format code properly with consistent indentation\n"
            "Output ONLY the Python code without any markdown formatting or explanation."
        )})
        
        gurobi_code = query_llm(messages, self.model_name)
        
        print("[CodeAgent] Gurobi code generation completed")
        return gurobi_code